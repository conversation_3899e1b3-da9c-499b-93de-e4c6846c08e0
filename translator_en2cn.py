#!/usr/bin/env python
# coding: utf-8

from model.transformer import build_transformer
from tokenization import PrepareData, MaskBatch
import time
import torch
import torch.nn as nn
import numpy as np
from tqdm import tqdm

import warnings
warnings.filterwarnings('ignore')

import matplotlib.pyplot as plt
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction

PAD = 0
UNK = 1
DEBUG = False

def get_config(debug=True):
    if debug:
        return{
            'lr': 1e-2,
            'batch_size': 64,
            'num_epochs': 10,
            'n_layer': 3,
            'h_num': 8,
            'd_model': 128,
            'd_ff': 256,
            'dropout': 0.1,
            'seq_len': 60,
            'train_file': 'data/en-cn/train_mini.txt',
            'dev_file': 'data/en-cn/dev_mini.txt',
            'save_file': 'save/models/model.pt'
        }
    else:
        return{
            'lr': 1e-4,
            'batch_size': 64,
            'num_epochs': 20,
            'n_layer': 6,
            'h_num': 8,
            'd_model': 256,
            'd_ff': 1024,
            'dropout': 0.1,
            'seq_len': 60,
            'train_file': 'data/en-cn/train.txt',
            'dev_file': 'data/en-cn/dev.txt',
            'save_file': 'save/models/model.pt'
        }

def get_model(config, vocab_src_len, vocab_tgt_len):
    model = build_transformer(vocab_src_len, vocab_tgt_len, config['seq_len'], config['seq_len'], config['d_model'], 
                              config['n_layer'], config['h_num'], config['dropout'], config['d_ff'])
    return model

config = get_config(DEBUG)
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device {device}")

data = PrepareData(config['train_file'], config['dev_file'], config['batch_size'], UNK, PAD)
src_vocab_size = len(data.en_word_dict); print(f"src_vocab_size {src_vocab_size}")
tgt_vocab_size = len(data.cn_word_dict); print(f"tgt_vocab_size {tgt_vocab_size}")

model = get_model(config, src_vocab_size, tgt_vocab_size).to(device)
loss_fn = nn.CrossEntropyLoss(ignore_index=PAD, label_smoothing=0.).to(device)
optimizer = torch.optim.Adam(model.parameters(), lr=config['lr'], eps=1e-9)

def casual_mask(size):
    mask = torch.triu(torch.ones(1, size, size), diagonal=1).type(torch.int)
    return mask == 0

def greedy_decode(model, source, source_mask, tokenizer_tgt, max_len, device):
    bos_id = tokenizer_tgt.get('BOS')
    eos_id = tokenizer_tgt.get('EOS')
    encoder_output = model.encode(source, source_mask)
    decoder_input = torch.empty(1,1).fill_(bos_id).type_as(source).to(device)
    while True:
        if decoder_input.size(1) == max_len:
            break
        decoder_mask = casual_mask(decoder_input.size(1)).type_as(source_mask).to(device)
        out = model.decode(encoder_output, source_mask, decoder_input, decoder_mask)
        prob = model.project(out[:, -1])
        _, next_word = torch.max(prob, dim=1)
        decoder_input = torch.cat([decoder_input, torch.empty(1,1). type_as(source).fill_(next_word.item()).to(device)], dim=1)
        if next_word == eos_id:
            break
    return decoder_input.squeeze(0)

def evaluate_loss_bleu(model, data, loss_fn, device, max_len):
    model.eval()
    total_loss = 0
    all_references = []
    all_hypotheses = []
    cc = SmoothingFunction()
    with torch.no_grad():
        for i, batch in enumerate(data.dev_data):
            encoder_input = batch.src.to(device)
            decoder_input = batch.tgt.to(device)
            encoder_mask = batch.src_mask.to(device)
            decoder_mask = batch.tgt_mask.to(device)
            label = batch.tgt_y.to(device)

            encoder_output = model.encode(encoder_input, encoder_mask)
            decoder_output = model.decode(encoder_output, encoder_mask, decoder_input, decoder_mask)
            proj_output = model.project(decoder_output)
            loss = loss_fn(proj_output.view(-1, proj_output.size(-1)), label.view(-1))
            total_loss += loss.item()

            # 真实 target（去掉特殊符号）
            ref = [data.cn_index_dict[w] for w in data.dev_cn[i] if w not in [PAD, UNK, data.cn_word_dict['BOS'], data.cn_word_dict['EOS']]]
            # 预测
            model_out = greedy_decode(model, encoder_input, encoder_mask, data.cn_word_dict, max_len, device)
            hypo = [data.cn_index_dict[model_out[j].item()] for j in range(1, model_out.size(0)) if model_out[j].item() != data.cn_word_dict['EOS']]

            # 统一格式：[参考翻译序列], 预测序列
            all_references.append([ref])
            all_hypotheses.append(hypo)

    avg_loss = total_loss / len(data.dev_data)
    bleu = np.mean([sentence_bleu(ref, hypo, smoothing_function=cc.method1) if len(hypo) > 0 else 0.0
                    for ref, hypo in zip(all_references, all_hypotheses)])
    return avg_loss, bleu

train_losses = []
valid_losses = []
bleu_scores = []

best_bleu = 0.0

print(">>>>>>> start train")
train_start = time.time()
initial_epoch = 0
global_step = 0

for epoch in range(initial_epoch, config['num_epochs']):
    model.train()
    batch_iterator = tqdm(data.train_data, desc = f'Processing epoch {epoch:02d}')
    epoch_loss = 0
    nbatch = 0
    for batch in batch_iterator:
        encoder_input = batch.src.to(device)
        decoder_input = batch.tgt.to(device)
        encoder_mask = batch.src_mask.to(device)
        decoder_mask = batch.tgt_mask.to(device)
        encoder_output = model.encode(encoder_input, encoder_mask)
        decoder_output = model.decode(encoder_output, encoder_mask, decoder_input, decoder_mask)
        proj_output = model.project(decoder_output)
        label = batch.tgt_y.to(device)
        loss = loss_fn(proj_output.view(-1, tgt_vocab_size), label.view(-1))
        batch_iterator.set_postfix({f"loss": f"{loss.item():6.3f}"})
        loss.backward()
        optimizer.step()
        optimizer.zero_grad()
        global_step += 1
        epoch_loss += loss.item()
        nbatch += 1
    train_losses.append(epoch_loss / nbatch)

    valid_loss, bleu = evaluate_loss_bleu(model, data, loss_fn, device, config['seq_len'])
    valid_losses.append(valid_loss)
    bleu_scores.append(bleu)
    print(f"Epoch {epoch}: valid_loss={valid_loss:.4f}, BLEU={bleu:.4f}")

    # 保存最佳BLEU模型
    if bleu > best_bleu:
        best_bleu = bleu
        torch.save(model.state_dict(), config['save_file'])
        print(f"Best BLEU updated. Model saved at {config['save_file']}")

print(f"<<<<<<< finished train, cost {time.time()-train_start:.4f} seconds")

# 绘图并保存
epochs = list(range(1, len(train_losses)+1))
plt.figure(figsize=(12, 5))
plt.subplot(1, 2, 1)
plt.plot(epochs, train_losses, label='Train Loss')
plt.plot(epochs, valid_losses, label='Valid Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.title('Train & Valid Loss')
plt.legend()

plt.subplot(1, 2, 2)
plt.plot(epochs, bleu_scores, label='BLEU')
plt.xlabel('Epoch')
plt.ylabel('BLEU Score')
plt.title('BLEU Curve')
plt.legend()
plt.tight_layout()
plt.savefig('loss_bleu_curve.png')
print('Training curves saved to loss_bleu_curve.png')
plt.show()
