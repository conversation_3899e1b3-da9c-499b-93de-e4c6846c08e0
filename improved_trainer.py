#!/usr/bin/env python
# coding: utf-8

"""
改进的Transformer训练脚本
包含训练/验证损失对比和BLEU分数变化曲线
"""

from model.transformer import build_transformer
from tokenization import PrepareData, MaskBatch
import time
import torch
import torch.nn as nn
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
import os
import jieba
import evaluate

import warnings
warnings.filterwarnings('ignore')

# 初始化参数
PAD = 0
UNK = 1
DEBUG = False

def get_config(debug=True):
    if debug:
        return{
            'lr': 1e-2,
            'batch_size': 64,
            'num_epochs': 20,
            'n_layer': 3,
            'h_num': 8,
            'd_model': 128,
            'd_ff': 256,
            'dropout': 0.1,
            'seq_len': 60,
            'train_file': 'data/en-cn/train_mini.txt',
            'dev_file': 'data/en-cn/dev_mini.txt',
            'save_file': 'save/models/improved_model.pt',
            'save_dir': 'save/models/',
            'plot_file': 'save/models/training_curves.png'
        }
    else:
        return{
            'lr': 1e-4,
            'batch_size': 64,
            'num_epochs': 30,
            'n_layer': 6,
            'h_num': 8,
            'd_model': 256,
            'd_ff': 1024,
            'dropout': 0.1,
            'seq_len': 60,
            'train_file': 'data/en-cn/train.txt',
            'dev_file': 'data/en-cn/dev.txt',
            'save_file': 'save/models/improved_model.pt',
            'save_dir': 'save/models/',
            'plot_file': 'save/models/training_curves.png'
        }

def get_model(config, src_vocab_size, tgt_vocab_size):
    model = build_transformer(src_vocab_size, tgt_vocab_size, config['seq_len'], config['seq_len'],
                             config['d_model'], config['n_layer'], config['h_num'],
                             config['dropout'], config['d_ff'])
    return model

def casual_mask(size):
    mask = torch.triu(torch.ones(1, size, size), diagonal=1).type(torch.int)
    return mask == 0

def greedy_decode(model, source, source_mask, tokenizer_tgt, max_len, device):
    bos_id = tokenizer_tgt.get('BOS')
    eos_id = tokenizer_tgt.get('EOS')

    encoder_output = model.encode(source, source_mask)
    decoder_input = torch.empty(1, 1).fill_(bos_id).type_as(source).to(device)

    while True:
        if decoder_input.size(1) == max_len:
            break

        decoder_mask = casual_mask(decoder_input.size(1)).type_as(source_mask).to(device)
        out = model.decode(encoder_output, source_mask, decoder_input, decoder_mask)
        prob = model.project(out[:, -1])
        _, next_word = torch.max(prob, dim=1)
        decoder_input = torch.cat([decoder_input, torch.empty(1, 1).type_as(source).fill_(next_word.item()).to(device)], dim=1)

        if next_word == eos_id:
            break

    return decoder_input.squeeze(0)

def calculate_validation_loss(model, data, loss_fn, tgt_vocab_size, device):
    """计算验证集损失"""
    model.eval()
    total_loss = 0.0
    num_batches = 0

    with torch.no_grad():
        for batch in data.dev_data:
            encoder_input = batch.src.to(device)
            decoder_input = batch.tgt.to(device)
            encoder_mask = batch.src_mask.to(device)
            decoder_mask = batch.tgt_mask.to(device)
            label = batch.tgt_y.to(device)

            encoder_output = model.encode(encoder_input, encoder_mask)
            decoder_output = model.decode(encoder_output, encoder_mask, decoder_input, decoder_mask)
            proj_output = model.project(decoder_output)

            loss = loss_fn(proj_output.view(-1, tgt_vocab_size), label.view(-1))
            total_loss += loss.item()
            num_batches += 1

    return total_loss / num_batches if num_batches > 0 else 0.0

def calculate_bleu_score(model, data, device, num_samples=30):
    """计算BLEU分数（使用少量样本以加快速度）"""
    try:
        model.eval()
        predictions = []
        references = []

        with torch.no_grad():
            for i, batch in enumerate(data.dev_data):
                if i >= num_samples:
                    break

                encoder_input = batch.src.to(device)
                encoder_mask = batch.src_mask.to(device)

                # 生成翻译
                model_out = greedy_decode(model, encoder_input, encoder_mask,
                                        data.cn_word_dict, 60, device)

                # 获取目标文本
                target_text = " ".join([data.cn_index_dict[w] for w in data.dev_cn[i]])

                # 生成的翻译文本
                model_out_text = []
                for j in range(1, model_out.size(0)):
                    sym = data.cn_index_dict[model_out[j].item()]
                    if sym != 'EOS':
                        model_out_text.append(sym)
                    else:
                        break

                pred_text = " ".join(model_out_text)

                # 对中文进行分词
                def chinese_tokenize(text):
                    text = text.replace('BOS', '').replace('EOS', '').strip()
                    words = list(jieba.cut(text, cut_all=False))
                    words = [w for w in words if w.strip()]
                    return ' '.join(words)

                pred_segmented = chinese_tokenize(pred_text)
                ref_segmented = chinese_tokenize(target_text)

                if pred_segmented.strip():
                    predictions.append(pred_segmented)
                    references.append([ref_segmented])

        if predictions and references:
            bleu = evaluate.load("bleu")
            results = bleu.compute(predictions=predictions, references=references)
            return results['bleu']
        else:
            return 0.0

    except Exception as e:
        print(f"BLEU计算错误: {e}")
        return 0.0

def run_validation_sample(model, data, device, num_examples=3):
    """运行验证样本展示"""
    model.eval()
    examples = []

    with torch.no_grad():
        for i, batch in enumerate(data.dev_data):
            if i >= num_examples:
                break

            encoder_input = batch.src.to(device)
            encoder_mask = batch.src_mask.to(device)

            model_out = greedy_decode(model, encoder_input, encoder_mask,
                                    data.cn_word_dict, 60, device)

            source_text = " ".join([data.en_index_dict[w] for w in data.dev_en[i]])
            target_text = " ".join([data.cn_index_dict[w] for w in data.dev_cn[i]])

            model_out_text = []
            for j in range(1, model_out.size(0)):
                sym = data.cn_index_dict[model_out[j].item()]
                if sym != 'EOS':
                    model_out_text.append(sym)
                else:
                    break

            examples.append({
                'source': source_text.replace('BOS ', '').replace(' EOS', ''),
                'target': target_text.replace('BOS ', '').replace(' EOS', ''),
                'predicted': ' '.join(model_out_text)
            })

    return examples

def main():
    print("改进的Transformer训练脚本")
    print("=" * 50)

    # 获取配置
    config = get_config(DEBUG)
    os.makedirs(config['save_dir'], exist_ok=True)

    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 数据预处理
    print("准备数据...")
    data = PrepareData(config['train_file'], config['dev_file'], config['batch_size'], UNK, PAD)
    src_vocab_size = len(data.en_word_dict)
    tgt_vocab_size = len(data.cn_word_dict)
    print(f"英文词汇表大小: {src_vocab_size}")
    print(f"中文词汇表大小: {tgt_vocab_size}")

    # 模型
    model = get_model(config, src_vocab_size, tgt_vocab_size).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型参数总数: {total_params:,}")

    # 优化器和损失函数
    optimizer = torch.optim.Adam(model.parameters(), lr=config['lr'], eps=1e-9)
    loss_fn = nn.CrossEntropyLoss(ignore_index=PAD, label_smoothing=0.1)

    # 训练记录
    train_losses = []
    valid_losses = []
    bleu_scores = []
    epoch_train_losses = []
    epoch_valid_losses = []

    print("\n开始训练...")
    train_start = time.time()

    for epoch in range(config['num_epochs']):
        print(f"\nEpoch {epoch+1}/{config['num_epochs']}")

        # 训练阶段
        model.train()
        epoch_train_loss = 0.0
        num_train_batches = 0

        batch_iterator = tqdm(data.train_data, desc=f'Training Epoch {epoch+1:02d}')

        for batch in batch_iterator:
            encoder_input = batch.src.to(device)
            decoder_input = batch.tgt.to(device)
            encoder_mask = batch.src_mask.to(device)
            decoder_mask = batch.tgt_mask.to(device)
            label = batch.tgt_y.to(device)

            encoder_output = model.encode(encoder_input, encoder_mask)
            decoder_output = model.decode(encoder_output, encoder_mask, decoder_input, decoder_mask)
            proj_output = model.project(decoder_output)

            loss = loss_fn(proj_output.view(-1, tgt_vocab_size), label.view(-1))

            train_losses.append(loss.item())
            epoch_train_loss += loss.item()
            num_train_batches += 1

            batch_iterator.set_postfix({'loss': f'{loss.item():.4f}'})

            loss.backward()
            optimizer.step()
            optimizer.zero_grad()

        # 计算平均训练损失
        avg_train_loss = epoch_train_loss / num_train_batches
        epoch_train_losses.append(avg_train_loss)

        # 验证阶段
        print("计算验证损失...")
        avg_valid_loss = calculate_validation_loss(model, data, loss_fn, tgt_vocab_size, device)
        epoch_valid_losses.append(avg_valid_loss)

        # 计算BLEU分数（每5个epoch计算一次以节省时间）
        if (epoch + 1) % 5 == 0 or epoch == 0:
            print("计算BLEU分数...")
            bleu_score = calculate_bleu_score(model, data, device)
            bleu_scores.append((epoch + 1, bleu_score))
            print(f"BLEU分数: {bleu_score:.4f}")

            # 显示翻译样例
            examples = run_validation_sample(model, data, device)
            print("\n翻译样例:")
            for i, ex in enumerate(examples):
                print(f"  {i+1}. 源文: {ex['source']}")
                print(f"     目标: {ex['target']}")
                print(f"     预测: {ex['predicted']}")

        print(f"训练损失: {avg_train_loss:.4f}, 验证损失: {avg_valid_loss:.4f}")

        # 保存检查点
        if (epoch + 1) % 5 == 0:
            checkpoint_path = os.path.join(config['save_dir'], f'improved_model_epoch_{epoch+1}.pt')
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': avg_train_loss,
                'valid_loss': avg_valid_loss,
                'config': config,
                'src_vocab_size': src_vocab_size,
                'tgt_vocab_size': tgt_vocab_size,
                'en_word_dict': data.en_word_dict,
                'cn_word_dict': data.cn_word_dict,
                'en_index_dict': data.en_index_dict,
                'cn_index_dict': data.cn_index_dict,
                'train_losses': train_losses,
                'valid_losses': valid_losses,
                'bleu_scores': bleu_scores
            }, checkpoint_path)
            print(f"检查点已保存: {checkpoint_path}")

    training_time = time.time() - train_start
    print(f"\n训练完成! 用时: {training_time:.2f}秒")

    # 保存最终模型
    torch.save({
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'config': config,
        'src_vocab_size': src_vocab_size,
        'tgt_vocab_size': tgt_vocab_size,
        'en_word_dict': data.en_word_dict,
        'cn_word_dict': data.cn_word_dict,
        'en_index_dict': data.en_index_dict,
        'cn_index_dict': data.cn_index_dict,
        'train_losses': train_losses,
        'valid_losses': valid_losses,
        'bleu_scores': bleu_scores,
        'epoch_train_losses': epoch_train_losses,
        'epoch_valid_losses': epoch_valid_losses
    }, config['save_file'])

    print(f"最终模型已保存: {config['save_file']}")

    # 绘制训练曲线
    plot_training_curves(epoch_train_losses, epoch_valid_losses, bleu_scores, config['plot_file'])

def plot_training_curves(train_losses, valid_losses, bleu_scores, save_path):
    """绘制训练和验证损失曲线，以及BLEU分数变化"""
    plt.figure(figsize=(15, 5))

    # 子图1: 训练和验证损失对比
    plt.subplot(1, 3, 1)
    epochs = range(1, len(train_losses) + 1)
    plt.plot(epochs, train_losses, 'b-', label='训练损失', linewidth=2)
    plt.plot(epochs, valid_losses, 'r-', label='验证损失', linewidth=2)
    plt.title('训练 vs 验证损失', fontsize=14, fontweight='bold')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 子图2: 损失差异
    plt.subplot(1, 3, 2)
    loss_diff = [v - t for t, v in zip(train_losses, valid_losses)]
    plt.plot(epochs, loss_diff, 'g-', label='验证损失 - 训练损失', linewidth=2)
    plt.title('过拟合监控', fontsize=14, fontweight='bold')
    plt.xlabel('Epoch')
    plt.ylabel('Loss Difference')
    plt.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 子图3: BLEU分数变化
    plt.subplot(1, 3, 3)
    if bleu_scores:
        bleu_epochs, bleu_values = zip(*bleu_scores)
        plt.plot(bleu_epochs, bleu_values, 'mo-', label='BLEU分数', linewidth=2, markersize=8)
        plt.title('BLEU分数变化', fontsize=14, fontweight='bold')
        plt.xlabel('Epoch')
        plt.ylabel('BLEU Score')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 标注最高BLEU分数
        max_bleu_idx = bleu_values.index(max(bleu_values))
        max_bleu_epoch = bleu_epochs[max_bleu_idx]
        max_bleu_score = bleu_values[max_bleu_idx]
        plt.annotate(f'最高: {max_bleu_score:.4f}',
                    xy=(max_bleu_epoch, max_bleu_score),
                    xytext=(max_bleu_epoch, max_bleu_score + 0.01),
                    arrowprops=dict(arrowstyle='->', color='red'),
                    fontsize=10, ha='center')

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"训练曲线已保存到: {save_path}")

    # 打印训练总结
    print("\n" + "="*50)
    print("训练总结")
    print("="*50)
    print(f"最终训练损失: {train_losses[-1]:.4f}")
    print(f"最终验证损失: {valid_losses[-1]:.4f}")
    if bleu_scores:
        max_bleu = max(bleu_scores, key=lambda x: x[1])
        print(f"最高BLEU分数: {max_bleu[1]:.4f} (Epoch {max_bleu[0]})")

    # 过拟合分析
    final_diff = valid_losses[-1] - train_losses[-1]
    if final_diff > 0.5:
        print("⚠️  警告: 可能存在过拟合 (验证损失明显高于训练损失)")
    elif final_diff < 0:
        print("✅ 良好: 验证损失低于训练损失，模型泛化良好")
    else:
        print("✅ 正常: 训练和验证损失差异在合理范围内")

if __name__ == "__main__":
    main()
